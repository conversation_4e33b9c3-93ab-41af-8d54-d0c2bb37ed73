class CupCustomizer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.mesh = null;
        this.animationId = null;
        this.isDragging = false;
        this.previousMousePosition = { x: 0, y: 0 };
        this.baseTexture = null;
        this.userTexture = null;
        this.combinedTexture = null;
        this.isFileDialogOpen = false;
        this.isProcessingFile = false;
        this.lastProcessedFile = null;

        // Modelos 3D dos copos
        this.cupModels = {
            '100ml': './img/copo_100ml/1941f2f3-db4b-47f5-a4a1-f845f4218cad.glb',
            '210ml': './img/copo_210ml/b8445f83-1511-4e74-9c79-4531d6e1f108.glb',
            '300ml': './img/copo_300ml/dd190afb-d75e-4377-b094-21d90b2a7800.glb',
            '500ml': './img/copo_500ml/5e6a3233-3d18-4394-97e8-2908456fd5ee.glb'
        };
        this.currentCupSize = '300ml';
        this.gltfLoader = null;

        this.init();
    }

    init() {
        this.bindEvents();
        this.createBaseTexture();
        this.initGLTFLoader();
    }

    initGLTFLoader() {
        setTimeout(() => {
            if (typeof THREE.GLTFLoader !== 'undefined') {
                this.gltfLoader = new THREE.GLTFLoader();
            }
        }, 100);
    }

    load3DCupModel(cupSize) {
        if (!this.gltfLoader || !this.cupModels[cupSize]) {
            this.createSimpleCup();
            return;
        }

        this.showLoadingIndicator();

        if (this.mesh) {
            this.scene.remove(this.mesh);
            this.mesh = null;
        }

        this.gltfLoader.load(
            this.cupModels[cupSize],
            (gltf) => {
                const model = gltf.scene;
                this.setup3DModel(model);

                if (this.combinedTexture || this.baseTexture) {
                    this.apply3DTexture(model, this.combinedTexture || this.baseTexture);
                }

                this.mesh = model;
                this.scene.add(this.mesh);
                this.hideLoadingIndicator();
            },
            null,
            (error) => {
                this.hideLoadingIndicator();
                this.createSimpleCup();
            }
        );
    }

    setup3DModel(model) {
        model.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;

                if (child.material) {
                    child.material = new THREE.MeshStandardMaterial({
                        map: child.material.map,
                        color: 0xffffff,
                        roughness: 0.3,
                        metalness: 0.0
                    });
                    child.material.needsUpdate = true;
                }
            }
        });

        // Posição e escala normais
        model.position.set(0, 0, 0);
        model.rotation.set(0, 0, 0);
        model.scale.set(1, 1, 1);
    }

    apply3DTexture(model, texture) {
        model.traverse((child) => {
            if (child.isMesh && child.material) {
                child.material.map = texture;
                child.material.needsUpdate = true;
            }
        });
    }

    createSimpleCup() {
        // Criar um copo simples quando os modelos 3D não funcionam
        const geometry = new THREE.CylinderGeometry(1.2, 1.0, 2.5, 32);
        const material = new THREE.MeshStandardMaterial({
            map: this.combinedTexture || this.baseTexture,
            color: 0xffffff,
            roughness: 0.3,
            metalness: 0.0
        });

        if (this.mesh) {
            this.scene.remove(this.mesh);
        }

        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.set(0, 0, 0);
        this.scene.add(this.mesh);
        this.hideLoadingIndicator();
    }

    bindEvents() {
        const downloadBtn = document.getElementById('downloadBtn');
        const uploadArea = document.getElementById('uploadArea');
        const uploadBtn = document.getElementById('uploadBtn');
        const fileInput = document.getElementById('fileInput');
        const removeImageBtn = document.getElementById('removeImage');

        downloadBtn.addEventListener('click', this.downloadTemplate.bind(this));
        uploadArea.addEventListener('click', this.triggerFileUpload.bind(this));
        uploadBtn.addEventListener('click', this.triggerFileUpload.bind(this));
        fileInput.addEventListener('change', this.handleFileUpload.bind(this));
        removeImageBtn.addEventListener('click', this.removeImage.bind(this));

        // Prevenir eventos extras no input de arquivo
        fileInput.addEventListener('focus', () => {
            this.isFileDialogOpen = true;
        });

        fileInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.isFileDialogOpen = false;
            }, 200);
        });
    }

    createBaseTexture() {
        // Criar textura padrão simples
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // Fundo branco
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 512, 512);

        this.baseTexture = new THREE.CanvasTexture(canvas);
        this.baseTexture.wrapS = THREE.RepeatWrapping;
        this.baseTexture.wrapT = THREE.RepeatWrapping;
    }


            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 2 + 0.5;
            const opacity = Math.random() * 0.1 + 0.02;

            ctx.fillStyle = `rgba(${Math.random() > 0.5 ? 255 : 200}, ${Math.random() > 0.5 ? 255 : 200}, ${Math.random() > 0.5 ? 255 : 200}, ${opacity})`;
            ctx.fillRect(x, y, size, size);
        }

        // Resetar composite operation
        ctx.globalCompositeOperation = 'source-over';

        // Criar textura Three.js com configurações PBR
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.encoding = THREE.sRGBEncoding;  // Encoding correto para PBR

        // Criar normal map para textura de papel
        const normalMap = this.createPaperNormalMap();

        console.log('✅ Textura PBR realista criada');
        this.baseTexture = texture;
        this.normalMap = normalMap;
        this.showDefault3DPreview();
    }

    createPaperNormalMap() {
        console.log('🗺️ Criando normal map para textura de papel...');

        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Criar ruído para simular fibras de papel
        const imageData = ctx.createImageData(canvas.width, canvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
            // Gerar ruído sutil para normal map
            const noise = (Math.random() - 0.5) * 0.3;
            const baseValue = 128 + noise * 20;

            data[i] = baseValue;     // R (X normal)
            data[i + 1] = baseValue; // G (Y normal)
            data[i + 2] = 255;       // B (Z normal - sempre para cima)
            data[i + 3] = 255;       // A (alpha)
        }

        ctx.putImageData(imageData, 0, 0);

        // Criar textura normal map
        const normalTexture = new THREE.CanvasTexture(canvas);
        normalTexture.wrapS = THREE.RepeatWrapping;
        normalTexture.wrapT = THREE.ClampToEdgeWrapping;
        normalTexture.minFilter = THREE.LinearFilter;
        normalTexture.magFilter = THREE.LinearFilter;

        console.log('✅ Normal map de papel criado');
        return normalTexture;
    }

    showDefault3DPreview() {
        console.log('🖥️ Iniciando preview 3D...');
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');

        if (!canvas) {
            console.error('❌ Canvas não encontrado!');
            return;
        }

        console.log('👁️ Mostrando canvas, escondendo placeholder');
        placeholder.classList.add('hidden');
        canvas.classList.remove('hidden');

        // Só inicializar se ainda não foi inicializado
        if (!this.scene) {
            console.log('🎬 Inicializando Three.js...');
            this.initThreeJS(canvas);
        }

        // Tentar carregar modelo 3D primeiro, usar procedural como fallback
        console.log('🎯 Tentando carregar modelo 3D...');
        this.load3DCupModel(this.currentCupSize);
        this.createFloor();

        // Posição da câmera otimizada para melhor visualização do modelo 3D
        this.camera.position.set(4, 1, 6);
        this.camera.lookAt(0, -0.5, 0);

        // Só iniciar animação se ainda não está rodando
        if (!this.animationId) {
            console.log('🎮 Iniciando animação...');
            this.animate();
        }
        console.log('✅ Preview 3D configurado!');
    }

    showPlaceholder() {
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');
        const loadingIndicator = document.getElementById('loadingIndicator');

        canvas.classList.add('hidden');
        loadingIndicator.classList.add('hidden');
        placeholder.classList.remove('hidden');
    }

    showLoadingIndicator() {
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');
        const loadingIndicator = document.getElementById('loadingIndicator');

        canvas.classList.add('hidden');
        placeholder.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');
    }

    hideLoadingIndicator() {
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');
        const loadingIndicator = document.getElementById('loadingIndicator');

        loadingIndicator.classList.add('hidden');
        placeholder.classList.add('hidden');
        canvas.classList.remove('hidden');
    }

    changeCupSize(event) {
        const newSize = event.target ? event.target.value : event;
        console.log(`🔄 Mudando tamanho do copo para: ${newSize}`);

        this.currentCupSize = newSize;

        // Atualizar label do botão de download
        const downloadLabel = document.getElementById('downloadLabel');
        if (downloadLabel) {
            downloadLabel.textContent = `Baixar gabarito (${newSize})`;
        }

        // Recriar o copo com as novas dimensões usando modelo 3D
        if (this.mesh && this.scene) {
            console.log(`📏 Carregando modelo 3D do ${newSize}`);
            this.load3DCupModel(newSize);
        }
    }

    downloadTemplate() {
        console.log('📄 Baixando gabarito padrão...');

        const pdfUrl = './gabarito/gabarito_copo_300ml.pdf';

        // Usar fetch para forçar download
        fetch(pdfUrl)
            .then(response => response.blob())
            .then(blob => {
                // Criar URL do blob
                const url = window.URL.createObjectURL(blob);

                // Criar link de download
                const link = document.createElement('a');
                link.href = url;
                link.download = 'gabarito_copo_300ml.pdf';
                link.style.display = 'none';

                // Adicionar ao DOM, clicar e remover
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Limpar URL do blob
                window.URL.revokeObjectURL(url);

                console.log('✅ Download concluído: gabarito_copo_300ml.pdf');
            })
            .catch(error => {
                console.error('❌ Erro no download:', error);
                // Fallback: abrir em nova aba
                window.open(pdfUrl, '_blank');
                console.log('📄 Abrindo em nova aba como fallback');
            });
    }

    triggerFileUpload(event) {
        // Prevenir duplo clique
        if (this.isFileDialogOpen || this.isProcessingFile) {
            console.log('Bloqueando trigger - dialog aberto ou processando');
            return;
        }

        // Não abrir file dialog se clicou no preview ou no botão de remover
        if (event && (event.target.closest('.upload-preview') || event.target.closest('.remove-image'))) {
            return;
        }

        console.log('Abrindo seletor de arquivo...');
        this.isFileDialogOpen = true;
        const fileInput = document.getElementById('fileInput');

        // Limpar valor anterior para garantir que o evento change dispare
        fileInput.value = '';
        fileInput.click();

        // Reset flag após um tempo mais longo
        setTimeout(() => {
            this.isFileDialogOpen = false;
        }, 1000);
    }

    handleFileUpload(event) {
        // Verificar se há arquivo selecionado
        const file = event.target.files[0];
        if (!file || !file.type.startsWith('image/')) {
            this.isFileDialogOpen = false;
            this.isProcessingFile = false;
            return;
        }

        // Prevenir processamento duplo
        if (this.isProcessingFile) {
            console.log('Já processando arquivo, ignorando...');
            return;
        }

        // Verificar se é o mesmo arquivo (prevenir reprocessamento)
        if (this.lastProcessedFile &&
            this.lastProcessedFile.name === file.name &&
            this.lastProcessedFile.size === file.size &&
            this.lastProcessedFile.lastModified === file.lastModified) {
            console.log('Mesmo arquivo já processado, ignorando...');
            return;
        }

        console.log('Processando novo arquivo:', file.name);
        this.isProcessingFile = true;
        this.isFileDialogOpen = false;
        this.lastProcessedFile = file;

        this.markStepCompleted();
        const imageUrl = URL.createObjectURL(file);
        this.showImagePreview(imageUrl);
        this.loadUserTexture(imageUrl);

        // Reset flag após processamento
        setTimeout(() => {
            this.isProcessingFile = false;
        }, 300);
    }

    loadUserTexture(imageUrl) {
        const loader = new THREE.TextureLoader();
        loader.load(imageUrl, (texture) => {
            texture.wrapS = THREE.RepeatWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;
            texture.minFilter = THREE.LinearFilter;
            texture.magFilter = THREE.LinearFilter;

            this.userTexture = texture;
            this.combineCupTextures();
        });
    }

    combineCupTextures() {
        if (!this.baseTexture || !this.userTexture) return;

        // Criar um canvas para combinar as texturas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Definir tamanho do canvas baseado na textura base
        canvas.width = this.baseTexture.image.width || 1024;
        canvas.height = this.baseTexture.image.height || 512;

        // Desenhar a textura base primeiro (fundo do copo)
        ctx.drawImage(this.baseTexture.image, 0, 0, canvas.width, canvas.height);

        // Configurar para sobrepor a imagem do usuário ENVOLTA do copo
        ctx.globalCompositeOperation = 'source-over';
        ctx.globalAlpha = 0.85; // Transparência para que a base apareça sutilmente

        // Desenhar a imagem do usuário envolta no copo (superfície externa)
        ctx.drawImage(this.userTexture.image, 0, 0, canvas.width, canvas.height);

        // Criar nova textura combinada
        const combinedTexture = new THREE.CanvasTexture(canvas);
        combinedTexture.wrapS = THREE.RepeatWrapping;
        combinedTexture.wrapT = THREE.ClampToEdgeWrapping;
        combinedTexture.minFilter = THREE.LinearFilter;
        combinedTexture.magFilter = THREE.LinearFilter;
        combinedTexture.needsUpdate = true;

        this.combinedTexture = combinedTexture;
        this.updateCupTexture(combinedTexture);
    }

    updateCupTexture(texture) {
        if (this.mesh) {
            if (this.loadedModel) {
                // Modelo 3D carregado - aplicar textura a todos os meshes
                this.apply3DTexture(this.mesh, texture);
            } else {
                // Geometria procedural - método original
                const exteriorMesh = this.mesh.children[0]; // Primeiro child é o exterior
                if (exteriorMesh && exteriorMesh.material) {
                    exteriorMesh.material.map = texture;
                    exteriorMesh.material.needsUpdate = true;
                }
            }
        }
    }

    showImagePreview(imageUrl) {
        const uploadPreview = document.getElementById('uploadPreview');
        const previewImage = document.getElementById('previewImage');

        uploadPreview.classList.remove('hidden');
        previewImage.src = imageUrl;
        previewImage.onload = () => {
            console.log('Preview image loaded successfully');
        };
    }

    removeImage(event) {
        event.stopPropagation();

        const uploadPreview = document.getElementById('uploadPreview');
        const previewImage = document.getElementById('previewImage');
        const fileInput = document.getElementById('fileInput');
        const step2Number = document.getElementById('step2Number');

        // Reset UI
        uploadPreview.classList.add('hidden');
        previewImage.src = '';
        fileInput.value = '';

        // Reset step
        step2Number.classList.remove('completed');

        // Reset texturas do usuário
        this.userTexture = null;
        this.combinedTexture = null;

        // Reset flags de controle
        this.isFileDialogOpen = false;
        this.isProcessingFile = false;
        this.lastProcessedFile = null;

        // Voltar para a textura base
        if (this.baseTexture && this.mesh) {
            this.updateCupTexture(this.baseTexture);
        }
    }

    markStepCompleted() {
        const step2Number = document.getElementById('step2Number');
        step2Number.classList.add('completed');
    }



    initThreeJS(canvas) {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf8f8f8);  // Fundo mais claro como na foto de referência

        this.camera = new THREE.PerspectiveCamera(50, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({
            canvas,
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });

        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);

        // CONFIGURAÇÕES PBR FOTORREALISTAS
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.physicallyCorrectLights = true;  // PBR lighting
        this.renderer.outputEncoding = THREE.sRGBEncoding;  // Cores corretas
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;  // Tom mapping cinematográfico
        this.renderer.toneMappingExposure = 1.2;  // Exposição natural

        this.setupLighting();
        this.setupMouseControls(canvas);
        this.setupResizeHandler(canvas);
    }

    setupLighting() {
        // ILUMINAÇÃO REALISTA PARA COPO BRANCO COMO NA FOTO DE REFERÊNCIA

        // Luz ambiente suave para simular luz difusa do ambiente
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        this.scene.add(ambientLight);

        // Luz direcional principal - simula luz natural vinda de cima/lateral
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
        directionalLight.position.set(5, 8, 3);
        directionalLight.castShadow = true;

        // Sombras suaves e realistas
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -10;
        directionalLight.shadow.camera.right = 10;
        directionalLight.shadow.camera.top = 10;
        directionalLight.shadow.camera.bottom = -10;
        directionalLight.shadow.bias = -0.0005;
        this.scene.add(directionalLight);

        // Luz de preenchimento para suavizar sombras (como na foto)
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-3, 4, 2);
        this.scene.add(fillLight);

        // Luz traseira suave para dar volume ao copo
        const backLight = new THREE.DirectionalLight(0xffffff, 0.2);
        backLight.position.set(0, 3, -5);
        this.scene.add(backLight);
    }



    createPaperCupWithSize(texture, cupSize) {
        const cupConfig = this.cupSizes[cupSize];
        if (!cupConfig) {
            console.error('❌ Configuração do copo não encontrada:', cupSize);
            return;
        }

        const { topRadius, bottomRadius, height } = cupConfig.dimensions;
        console.log(`🥤 Criando copo ${cupSize}:`, { topRadius, bottomRadius, height });

        if (this.mesh) {
            console.log('🗑️ Removendo copo anterior');
            this.scene.remove(this.mesh);
        }

        const cupGroup = new THREE.Group();
        console.log('📦 Grupo do copo criado');

        // EXTERIOR do copo com PBR FOTORREALISTA + NORMAL MAP
        const exteriorGeometry = new THREE.CylinderGeometry(topRadius, bottomRadius, height, 64);
        const exteriorMaterial = new THREE.MeshStandardMaterial({
            map: texture,
            normalMap: this.normalMap,  // Normal map para textura de papel
            color: 0xfafafa,           // Branco levemente acinzentado
            roughness: 0.85,           // Papel é bem rugoso (0.8-0.9)
            metalness: 0.0,            // Papel não é metálico
            transparent: false,
            side: THREE.FrontSide,
            normalScale: new THREE.Vector2(0.3, 0.3)  // Intensidade do normal map
        });
        const exteriorMesh = new THREE.Mesh(exteriorGeometry, exteriorMaterial);
        exteriorMesh.castShadow = true;
        exteriorMesh.receiveShadow = true;
        cupGroup.add(exteriorMesh);

        // INTERIOR com PBR FOTORREALISTA - sombra natural
        const interiorTopRadius = topRadius - 0.02;
        const interiorBottomRadius = bottomRadius - 0.02;
        const interiorHeight = height - 0.05;
        const interiorGeometry = new THREE.CylinderGeometry(interiorTopRadius, interiorBottomRadius, interiorHeight, 64);
        const interiorMaterial = new THREE.MeshStandardMaterial({
            color: 0x2a2a2a,    // Cinza muito escuro natural
            side: THREE.BackSide,
            roughness: 0.95,    // Muito rugoso para absorver luz
            metalness: 0.0,     // Não metálico
            transparent: false,
            opacity: 1.0
        });
        const interiorMesh = new THREE.Mesh(interiorGeometry, interiorMaterial);
        interiorMesh.position.y = 0.025;
        cupGroup.add(interiorMesh);

        // SUPERFÍCIE INTERNA DO TOPO - PBR realista
        const innerSurfaceGeometry = new THREE.CircleGeometry(interiorTopRadius, 64);
        const innerSurfaceMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a1a1a,    // Muito escuro natural
            roughness: 0.98,    // Extremamente rugoso
            metalness: 0.0,     // Não metálico
            transparent: false,
            opacity: 1.0
        });
        const innerSurfaceMesh = new THREE.Mesh(innerSurfaceGeometry, innerSurfaceMaterial);
        innerSurfaceMesh.rotation.x = -Math.PI / 2;
        innerSurfaceMesh.position.y = height / 2 - 0.1;
        cupGroup.add(innerSurfaceMesh);

        // FUNDO INTERNO DO COPO - PBR realista
        const innerBottomGeometry = new THREE.CircleGeometry(interiorBottomRadius, 64);
        const innerBottomMaterial = new THREE.MeshStandardMaterial({
            color: 0x0f0f0f,    // Quase preto natural
            roughness: 0.98,    // Extremamente rugoso
            metalness: 0.0      // Não metálico
        });
        const innerBottomMesh = new THREE.Mesh(innerBottomGeometry, innerBottomMaterial);
        innerBottomMesh.rotation.x = -Math.PI / 2;
        innerBottomMesh.position.y = -(height / 2) + 0.05;
        cupGroup.add(innerBottomMesh);

        // BORDA do copo - PBR realista
        const rimTopRadius = topRadius + 0.02;
        const rimGeometry = new THREE.CylinderGeometry(rimTopRadius, topRadius, 0.03, 64);
        const rimMaterial = new THREE.MeshStandardMaterial({
            color: 0xf0f0f0,    // Cinza claro como o corpo
            roughness: 0.85,    // Rugoso como papel
            metalness: 0.0      // Não metálico
        });
        const rimMesh = new THREE.Mesh(rimGeometry, rimMaterial);
        rimMesh.position.y = (height / 2) + 0.015;
        rimMesh.castShadow = true;
        cupGroup.add(rimMesh);

        // FUNDO EXTERNO do copo - PBR realista
        const bottomGeometry = new THREE.CircleGeometry(bottomRadius, 64);
        const bottomMaterial = new THREE.MeshStandardMaterial({
            color: 0xe8e8e8,    // Cinza claro para o fundo
            roughness: 0.85,    // Rugoso como papel
            metalness: 0.0      // Não metálico
        });
        const bottomMesh = new THREE.Mesh(bottomGeometry, bottomMaterial);
        bottomMesh.rotation.x = -Math.PI / 2;
        bottomMesh.position.y = -(height / 2);
        bottomMesh.receiveShadow = true;
        cupGroup.add(bottomMesh);

        this.mesh = cupGroup;
        this.mesh.position.y = 0;
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        this.scene.add(this.mesh);
        console.log(`✅ Copo ${cupSize} adicionado à cena!`, this.mesh);
    }

    createPaperCup(texture) {
        console.log('🥤 Criando copo de papel...');
        if (this.mesh) {
            console.log('🗑️ Removendo copo anterior');
            this.scene.remove(this.mesh);
        }

        const cupGroup = new THREE.Group();
        console.log('📦 Grupo do copo criado');

        // COPO MAIS SIMÉTRICO - Menos funil, mais cilíndrico
        // Topo: 1.3, Base: 1.1 (mais simétrico), Altura: 2.8
        const exteriorGeometry = new THREE.CylinderGeometry(1.3, 1.1, 2.8, 64);
        const exteriorMaterial = new THREE.MeshPhongMaterial({
            map: texture,
            color: 0xf8f8f8,  // Cinza muito claro para parecer papel
            shininess: 4,     // Pouco brilho para parecer papel fosco
            transparent: false,
            roughness: 0.8    // Superfície mais rugosa como papel
        });
        const exteriorMesh = new THREE.Mesh(exteriorGeometry, exteriorMaterial);
        exteriorMesh.castShadow = true;
        exteriorMesh.receiveShadow = true;
        cupGroup.add(exteriorMesh);

        // Interior com sombra realista MUITO MAIS ESCURA (ligeiramente menor)
        const interiorGeometry = new THREE.CylinderGeometry(1.28, 1.08, 2.75, 64);
        const interiorMaterial = new THREE.MeshPhongMaterial({
            color: 0x808080,  // CINZA MUITO MAIS ESCURO como na foto original
            side: THREE.BackSide,
            shininess: 1,     // Quase sem brilho
            transparent: false,
            opacity: 1.0      // Totalmente opaco para sombra forte
        });
        const interiorMesh = new THREE.Mesh(interiorGeometry, interiorMaterial);
        interiorMesh.position.y = 0.025;
        cupGroup.add(interiorMesh);

        // PREENCHIMENTO DO INTERIOR - Superfície interna com sombra
        const innerSurfaceGeometry = new THREE.CircleGeometry(1.28, 64); // Círculo no topo
        const innerSurfaceMaterial = new THREE.MeshPhongMaterial({
            color: 0x606060,  // Cinza ainda mais escuro para o fundo interno
            shininess: 0,     // Sem brilho
            transparent: true,
            opacity: 0.8      // Levemente transparente para profundidade
        });
        const innerSurfaceMesh = new THREE.Mesh(innerSurfaceGeometry, innerSurfaceMaterial);
        innerSurfaceMesh.rotation.x = -Math.PI / 2; // Virar para ficar horizontal
        innerSurfaceMesh.position.y = 1.3; // No topo do copo (abertura)
        cupGroup.add(innerSurfaceMesh);

        // FUNDO INTERNO DO COPO - Superfície no fundo
        const innerBottomGeometry = new THREE.CircleGeometry(1.08, 64); // Círculo no fundo
        const innerBottomMaterial = new THREE.MeshPhongMaterial({
            color: 0x505050,  // Cinza muito escuro para o fundo
            shininess: 0      // Sem brilho
        });
        const innerBottomMesh = new THREE.Mesh(innerBottomGeometry, innerBottomMaterial);
        innerBottomMesh.rotation.x = -Math.PI / 2; // Virar para ficar horizontal
        innerBottomMesh.position.y = -1.35; // No fundo interno do copo
        cupGroup.add(innerBottomMesh);

        // Borda do copo (mais realista, cor similar ao corpo)
        const rimGeometry = new THREE.CylinderGeometry(1.32, 1.3, 0.03, 64);
        const rimMaterial = new THREE.MeshPhongMaterial({
            color: 0xf0f0f0,  // Cinza claro como o corpo
            shininess: 8      // Brilho moderado
        });
        const rimMesh = new THREE.Mesh(rimGeometry, rimMaterial);
        rimMesh.position.y = 1.415; // Ajustado para nova altura
        rimMesh.castShadow = true;
        cupGroup.add(rimMesh);

        // Fundo do copo (cor realista, ajustado para nova base)
        const bottomGeometry = new THREE.CircleGeometry(1.1, 64);
        const bottomMaterial = new THREE.MeshPhongMaterial({
            color: 0xe8e8e8,  // Cinza claro para o fundo
            shininess: 3      // Pouco brilho para parecer papel
        });
        const bottomMesh = new THREE.Mesh(bottomGeometry, bottomMaterial);
        bottomMesh.rotation.x = -Math.PI / 2;
        bottomMesh.position.y = -1.4; // Ajustado para nova altura
        bottomMesh.receiveShadow = true;
        cupGroup.add(bottomMesh);

        this.mesh = cupGroup;
        this.mesh.position.y = 0;
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        this.scene.add(this.mesh);
        console.log('✅ Copo adicionado à cena!', this.mesh);
    }

    createFloor() {
        const planeGeometry = new THREE.PlaneGeometry(20, 20);
        const planeMaterial = new THREE.ShadowMaterial({
            opacity: 0.15
        });
        const plane = new THREE.Mesh(planeGeometry, planeMaterial);
        plane.rotation.x = -Math.PI / 2;
        plane.position.y = -2.0; // Ajustado para o modelo 3D
        plane.receiveShadow = true;
        this.scene.add(plane);
    }

    setupMouseControls(canvas) {
        // Mouse events
        canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        canvas.addEventListener('mouseleave', this.onMouseUp.bind(this)); // Para quando sair do canvas
        canvas.addEventListener('wheel', this.onMouseWheel.bind(this));

        // Touch events
        canvas.addEventListener('touchstart', this.onTouchStart.bind(this), { passive: false });
        canvas.addEventListener('touchmove', this.onTouchMove.bind(this), { passive: false });
        canvas.addEventListener('touchend', this.onTouchEnd.bind(this), { passive: false });
        canvas.addEventListener('touchcancel', this.onTouchEnd.bind(this), { passive: false });

        // Garantir que o canvas pode receber foco
        canvas.style.touchAction = 'none';
        canvas.tabIndex = 0;
    }

    onMouseDown(event) {
        event.preventDefault();
        this.isDragging = true;
        this.previousMousePosition = {
            x: event.clientX,
            y: event.clientY
        };
    }

    onMouseMove(event) {
        if (!this.isDragging || !this.mesh) return;
        event.preventDefault();

        const deltaX = event.clientX - this.previousMousePosition.x;
        const deltaY = event.clientY - this.previousMousePosition.y;

        // DETERMINAR SE É MOVIMENTO HORIZONTAL OU VERTICAL (NÃO AMBOS)
        const absX = Math.abs(deltaX);
        const absY = Math.abs(deltaY);

        if (absX > absY) {
            // MOVIMENTO HORIZONTAL: 360° COMPLETO - APENAS GIRA NO EIXO Y
            this.mesh.rotation.y += deltaX * 0.01;
        } else {
            // MOVIMENTO VERTICAL: INCLINAR o copo para frente/trás (limitado)
            this.mesh.rotation.x += deltaY * 0.005;

            // Limitar inclinação para não virar de cabeça para baixo
            this.mesh.rotation.x = Math.max(-0.5, Math.min(0.5, this.mesh.rotation.x));
        }

        this.previousMousePosition.x = event.clientX;
        this.previousMousePosition.y = event.clientY;
    }

    onMouseUp() {
        this.isDragging = false;
    }

    onMouseWheel(event) {
        if (!this.camera) return;

        event.preventDefault();
        const zoomSpeed = 0.5;
        const currentDistance = this.camera.position.length();
        const newDistance = currentDistance + (event.deltaY * zoomSpeed * 0.01);

        // Limitar zoom
        const minDistance = 4;
        const maxDistance = 12;
        const clampedDistance = Math.max(minDistance, Math.min(maxDistance, newDistance));

        // Manter a direção da câmera
        const direction = this.camera.position.clone().normalize();
        this.camera.position.copy(direction.multiplyScalar(clampedDistance));
    }

    onTouchStart(event) {
        event.preventDefault();
        const touch = event.touches[0];
        this.isDragging = true;
        this.previousMousePosition = {
            x: touch.clientX,
            y: touch.clientY
        };
    }

    onTouchMove(event) {
        event.preventDefault();
        if (!this.isDragging || !this.mesh || event.touches.length !== 1) return;

        const touch = event.touches[0];
        const deltaX = touch.clientX - this.previousMousePosition.x;
        const deltaY = touch.clientY - this.previousMousePosition.y;

        // DETERMINAR SE É MOVIMENTO HORIZONTAL OU VERTICAL (NÃO AMBOS) - igual ao mouse
        const absX = Math.abs(deltaX);
        const absY = Math.abs(deltaY);

        if (absX > absY) {
            // MOVIMENTO HORIZONTAL: 360° COMPLETO - APENAS GIRA NO EIXO Y
            this.mesh.rotation.y += deltaX * 0.01;
        } else {
            // MOVIMENTO VERTICAL: INCLINAR o copo para frente/trás (limitado) - igual ao mouse
            this.mesh.rotation.x += deltaY * 0.005;

            // Limitar inclinação para não virar de cabeça para baixo
            this.mesh.rotation.x = Math.max(-0.5, Math.min(0.5, this.mesh.rotation.x));
        }

        this.previousMousePosition.x = touch.clientX;
        this.previousMousePosition.y = touch.clientY;
    }

    onTouchEnd(event) {
        event.preventDefault();
        this.isDragging = false;
    }

    setupResizeHandler(canvas) {
        const resizeObserver = new ResizeObserver(() => {
            if (this.camera && this.renderer) {
                this.camera.aspect = canvas.clientWidth / canvas.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            }
        });
        resizeObserver.observe(canvas);
    }

    animate() {
        this.animationId = requestAnimationFrame(this.animate.bind(this));

        // Rotação automática suave do copo (opcional)
        if (this.mesh && !this.isDragging) {
            this.mesh.rotation.y += 0.005; // Rotação lenta automática
        }

        this.renderer.render(this.scene, this.camera);
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new CupCustomizer();
});

window.addEventListener('beforeunload', () => {
    if (window.cupCustomizer) {
        window.cupCustomizer.destroy();
    }
});
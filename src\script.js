class CupCustomizer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.mesh = null;
        this.animationId = null;
        this.isDragging = false;
        this.previousMousePosition = { x: 0, y: 0 };
        this.baseTexture = null;
        this.userTexture = null;
        this.combinedTexture = null;
        this.isFileDialogOpen = false;
        this.isProcessingFile = false;

        // Modelos 3D dos copos
        this.cupModels = {
            '100ml': './img/copo_100ml/1941f2f3-db4b-47f5-a4a1-f845f4218cad.glb',
            '210ml': './img/copo_210ml/b8445f83-1511-4e74-9c79-4531d6e1f108.glb',
            '300ml': './img/copo_300ml/dd190afb-d75e-4377-b094-21d90b2a7800.glb',
            '500ml': './img/copo_500ml/5e6a3233-3d18-4394-97e8-2908456fd5ee.glb'
        };
        this.currentCupSize = '300ml';
        this.gltfLoader = null;

        this.init();
    }

    init() {
        this.bindEvents();
        this.createBaseTexture();
        this.initGLTFLoader();
        // Aguardar um pouco para garantir que tudo carregou
        setTimeout(() => {
            this.showDefault3DPreview();
        }, 500);
    }

    initGLTFLoader() {
        setTimeout(() => {
            if (typeof THREE.GLTFLoader !== 'undefined') {
                this.gltfLoader = new THREE.GLTFLoader();
            }
        }, 100);
    }

    createBaseTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');
        
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 512, 512);
        
        this.baseTexture = new THREE.CanvasTexture(canvas);
        this.baseTexture.wrapS = THREE.RepeatWrapping;
        this.baseTexture.wrapT = THREE.RepeatWrapping;
    }

    load3DCupModel(cupSize) {
        // Por enquanto, sempre usar o copo simples até os modelos 3D funcionarem
        this.createSimpleCup();
        return;

        // Código original comentado temporariamente
        /*
        if (!this.gltfLoader || !this.cupModels[cupSize]) {
            this.createSimpleCup();
            return;
        }

        this.showLoadingIndicator();

        if (this.mesh) {
            this.scene.remove(this.mesh);
            this.mesh = null;
        }

        this.gltfLoader.load(
            this.cupModels[cupSize],
            (gltf) => {
                const model = gltf.scene;
                this.setup3DModel(model);

                if (this.combinedTexture || this.baseTexture) {
                    this.apply3DTexture(model, this.combinedTexture || this.baseTexture);
                }

                this.mesh = model;
                this.scene.add(this.mesh);
                this.hideLoadingIndicator();
            },
            null,
            () => {
                this.hideLoadingIndicator();
                this.createSimpleCup();
            }
        );
        */
    }

    setup3DModel(model) {
        model.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;

                if (child.material) {
                    child.material = new THREE.MeshStandardMaterial({
                        map: child.material.map,
                        color: 0xffffff,
                        roughness: 0.3,
                        metalness: 0.0
                    });
                    child.material.needsUpdate = true;
                }
            }
        });

        model.position.set(0, 0, 0);
        model.rotation.set(0, 0, 0);
        model.scale.set(1, 1, 1);
    }

    apply3DTexture(model, texture) {
        model.traverse((child) => {
            if (child.isMesh && child.material) {
                child.material.map = texture;
                child.material.needsUpdate = true;
            }
        });
    }

    createSimpleCup() {
        console.log('🥤 Criando copo simples...');
        const geometry = new THREE.CylinderGeometry(1.2, 1.0, 2.5, 32);
        const material = new THREE.MeshStandardMaterial({
            map: this.combinedTexture || this.baseTexture,
            color: 0xffffff,
            roughness: 0.3,
            metalness: 0.0
        });

        if (this.mesh) {
            this.scene.remove(this.mesh);
            console.log('🗑️ Removendo copo anterior');
        }

        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.set(0, 0, 0);
        this.scene.add(this.mesh);
        console.log('✅ Copo simples criado e adicionado à cena!');
        this.hideLoadingIndicator();
    }

    bindEvents() {
        const downloadBtn = document.getElementById('downloadBtn');
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const removeBtn = document.querySelector('.remove-image');

        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadTemplate());
        }

        if (uploadArea) {
            uploadArea.addEventListener('click', (e) => this.triggerFileUpload(e));
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }

        if (removeBtn) {
            removeBtn.addEventListener('click', (e) => this.removeImage(e));
        }
    }

    showDefault3DPreview() {
        console.log('🎯 Iniciando preview 3D...');
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');

        if (!canvas || !placeholder) {
            console.error('❌ Canvas ou placeholder não encontrado!');
            return;
        }

        console.log('✅ Canvas e placeholder encontrados');
        this.initThreeJS(canvas);
        this.load3DCupModel(this.currentCupSize);

        placeholder.classList.add('hidden');
        canvas.classList.remove('hidden');
        console.log('✅ Preview 3D configurado!');
    }

    showPlaceholder() {
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');
        const loadingIndicator = document.getElementById('loadingIndicator');

        canvas.classList.add('hidden');
        loadingIndicator.classList.add('hidden');
        placeholder.classList.remove('hidden');
    }

    showLoadingIndicator() {
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');
        const loadingIndicator = document.getElementById('loadingIndicator');

        canvas.classList.add('hidden');
        placeholder.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');
    }

    hideLoadingIndicator() {
        const canvas = document.getElementById('cupCanvas');
        const placeholder = document.getElementById('cupPlaceholder');
        const loadingIndicator = document.getElementById('loadingIndicator');

        loadingIndicator.classList.add('hidden');
        placeholder.classList.add('hidden');
        canvas.classList.remove('hidden');
    }

    downloadTemplate() {
        const pdfUrl = './gabarito/gabarito_copo_300ml.pdf';
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.download = 'gabarito_copo_300ml.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    triggerFileUpload(event) {
        // Prevenir duplo clique
        if (this.isFileDialogOpen || this.isProcessingFile) {
            return;
        }

        if (event && (event.target.closest('.upload-preview') || event.target.closest('.remove-image'))) {
            return;
        }

        this.isFileDialogOpen = true;
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.click();
        }

        // Reset flag após um tempo
        setTimeout(() => {
            this.isFileDialogOpen = false;
        }, 1000);
    }

    handleFileUpload(event) {
        // Verificar se já está processando
        if (this.isProcessingFile) {
            return;
        }

        const file = event.target.files[0];
        if (!file || !file.type.startsWith('image/')) {
            this.isFileDialogOpen = false;
            this.isProcessingFile = false;
            return;
        }

        this.isProcessingFile = true;
        const reader = new FileReader();
        reader.onload = (e) => {
            this.showImagePreview(e.target.result);
            this.loadUserTexture(e.target.result);
            this.markStepCompleted();

            // Reset flag após processamento
            setTimeout(() => {
                this.isProcessingFile = false;
            }, 300);
        };
        reader.readAsDataURL(file);
    }

    loadUserTexture(imageUrl) {
        const loader = new THREE.TextureLoader();
        loader.load(imageUrl, (texture) => {
            texture.wrapS = THREE.RepeatWrapping;
            texture.wrapT = THREE.RepeatWrapping;
            texture.minFilter = THREE.LinearFilter;
            texture.magFilter = THREE.LinearFilter;

            this.userTexture = texture;
            this.combineCupTextures();
        });
    }

    combineCupTextures() {
        if (!this.baseTexture || !this.userTexture) return;

        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // Desenhar textura base
        const baseCanvas = this.baseTexture.image;
        ctx.drawImage(baseCanvas, 0, 0, 512, 512);

        // Desenhar imagem do usuário
        const userImage = this.userTexture.image;
        ctx.drawImage(userImage, 0, 0, 512, 512);

        const combinedTexture = new THREE.CanvasTexture(canvas);
        combinedTexture.wrapS = THREE.RepeatWrapping;
        combinedTexture.wrapT = THREE.RepeatWrapping;

        this.combinedTexture = combinedTexture;
        this.updateCupTexture(combinedTexture);
    }

    updateCupTexture(texture) {
        if (this.mesh) {
            if (this.mesh.traverse) {
                this.mesh.traverse((child) => {
                    if (child.isMesh && child.material) {
                        child.material.map = texture;
                        child.material.needsUpdate = true;
                    }
                });
            } else if (this.mesh.material) {
                this.mesh.material.map = texture;
                this.mesh.material.needsUpdate = true;
            }
        }
    }

    showImagePreview(imageUrl) {
        const uploadPreview = document.getElementById('uploadPreview');
        const previewImage = document.getElementById('previewImage');

        previewImage.src = imageUrl;
        uploadPreview.classList.remove('hidden');
    }

    removeImage(event) {
        event.stopPropagation();

        const uploadPreview = document.getElementById('uploadPreview');
        const previewImage = document.getElementById('previewImage');
        const fileInput = document.getElementById('fileInput');

        uploadPreview.classList.add('hidden');
        previewImage.src = '';
        fileInput.value = '';

        this.userTexture = null;
        this.combinedTexture = null;

        if (this.mesh) {
            this.updateCupTexture(this.baseTexture);
        }
    }

    markStepCompleted() {
        const step2Number = document.getElementById('step2Number');
        if (step2Number) {
            step2Number.classList.add('completed');
        }
    }

    initThreeJS(canvas) {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf8f8f8);

        this.camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
        this.camera.position.set(0, 0, 5);

        this.renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        this.setupLighting();
        this.setupMouseControls(canvas);
        this.animate();
    }

    setupLighting() {
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }

    setupMouseControls(canvas) {
        canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
    }

    onMouseDown(event) {
        event.preventDefault();
        this.isDragging = true;
        this.previousMousePosition = {
            x: event.clientX,
            y: event.clientY
        };
    }

    onMouseMove(event) {
        if (!this.isDragging || !this.mesh) return;
        event.preventDefault();

        const deltaX = event.clientX - this.previousMousePosition.x;
        const deltaY = event.clientY - this.previousMousePosition.y;

        this.mesh.rotation.y += deltaX * 0.01;
        this.mesh.rotation.x += deltaY * 0.01;

        this.previousMousePosition.x = event.clientX;
        this.previousMousePosition.y = event.clientY;
    }

    onMouseUp() {
        this.isDragging = false;
    }

    onMouseWheel(event) {
        if (!this.camera) return;
        event.preventDefault();

        const zoomSpeed = 0.5;
        const currentDistance = this.camera.position.length();
        const newDistance = currentDistance + (event.deltaY * zoomSpeed * 0.01);

        const minDistance = 3;
        const maxDistance = 10;
        const clampedDistance = Math.max(minDistance, Math.min(maxDistance, newDistance));

        const direction = this.camera.position.clone().normalize();
        this.camera.position.copy(direction.multiplyScalar(clampedDistance));
    }

    animate() {
        this.animationId = requestAnimationFrame(this.animate.bind(this));
        this.renderer.render(this.scene, this.camera);
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new CupCustomizer();
});

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f0f0f0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 40px rgba(0,0,0,0.12);
    max-width: 520px;
    width: 100%;
    overflow: hidden;
}

.step {
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.step:last-child {
    border-bottom: none;
}

.step.completed .step-number {
    background: #28a745;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.step-number.completed {
    background: #28a745;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}



.download-btn {
    background: #007bff;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    min-width: 150px;
}

.download-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.download-btn svg {
    width: 16px;
    height: 16px;
}

.upload-section {
    padding: 20px 24px;
}

.upload-area {
    border: 2px dashed #ff8c42;
    border-radius: 12px;
    padding: 24px;
    background: #fff8f4;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.upload-area:hover {
    border-color: #ff6b1a;
    background: #fff3eb;
}

.upload-area input {
    position: absolute;
    inset: 0;
    opacity: 0;
    cursor: pointer;
}

.upload-content {
    flex: 1;
}

.upload-preview {
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #ddd;
    flex-shrink: 0;
    position: relative;
}

.upload-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(0,0,0,0.7);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 1;
}

.remove-image:hover {
    background: rgba(0,0,0,0.9);
}

.upload-btn {
    background: #ff8c42;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 8px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.upload-btn:hover {
    background: #ff6b1a;
}

.upload-constraints {
    color: #666;
    font-size: 14px;
}

.preview-section {
    padding: 24px;
    background: #f0f0f0;
    text-align: center;
}

.cup-preview {
    width: 100%;
    max-width: 450px;
    height: 450px;
    margin: 0 auto;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.cup-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab; /* 👆 Cursor de mão indicando que pode arrastar */
    touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    pointer-events: auto;
    transition: cursor 0.1s ease; /* Transição suave do cursor */
}

.cup-canvas:active {
    cursor: grabbing; /* ✊ Cursor de mão fechada quando arrastando */
}

.cup-canvas:hover {
    cursor: grab; /* Garantir que sempre mostre o cursor de arrastar */
}

.cup-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: #aaa;
}

.placeholder-icon {
    font-size: 52px;
    opacity: 0.6;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    opacity: 0.8;
}

.hidden {
    display: none !important;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
    }
    
    .cup-preview {
        height: 300px;
    }
    
    .step {
        padding: 16px 20px;
    }
    
    .upload-section {
        padding: 16px 20px;
    }
    
    .preview-section {
        padding: 20px;
    }

    .upload-area {
        flex-direction: column;
        text-align: center;
    }

    .upload-preview {
        width: 100px;
        height: 60px;
    }
}
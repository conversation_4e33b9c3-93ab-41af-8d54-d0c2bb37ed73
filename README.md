# Personalizador de Copo 3D

Um aplicativo web interativo para personalização de copos com visualização 3D em tempo real.

## Características

- ✅ Interface moderna e responsiva
- ✅ **Textura base padrão**: Copo 3D aparece automaticamente com textura base
- ✅ **Combinação de texturas**: Imagem do usuário é sobreposta na textura base
- ✅ Upload de imagens (JPG/PNG até 10MB)
- ✅ Visualização 3D interativa do copo
- ✅ Controles de mouse e touch para rotação
- ✅ Zoom com scroll do mouse
- ✅ Preview da imagem aplicada no copo
- ✅ Remoção de imagem com reset para textura base
- ✅ Sombras e iluminação realista
- ✅ Responsivo para dispositivos móveis

## Tecnologias Utilizadas

- **HTML5** - Estrutura da aplicação
- **CSS3** - Estilização e layout responsivo
- **JavaScript ES6+** - Lógica da aplicação
- **Three.js** - Renderização 3D
- **WebGL** - Aceleração gráfica

## Como Usar

1. **Visualização Inicial**: O copo 3D aparece automaticamente com textura base
2. **Baixar Gabarito**: Clique no botão de download para obter o template
3. **Upload da Arte**: Clique em "Enviar arquivo" e selecione sua imagem
   - A imagem será **sobreposta** na textura base do copo
   - Use o botão "×" para remover e voltar à textura base
4. **Interagir**: Use mouse ou touch para controlar o copo 3D:
   - **Arrastar**: Rotacionar o copo
   - **Scroll**: Zoom in/out
   - **Touch**: Rotação em dispositivos móveis

## Estrutura do Projeto

```
src/
├── index.html      # Página principal
├── style.css       # Estilos e layout
├── script.js       # Lógica JavaScript e Three.js
└── img/
    └── cup_texture.jpg  # Textura base do copo (opcional)
```

## Executar Localmente

1. Clone ou baixe o projeto
2. Navegue até a pasta `src`
3. Execute um servidor HTTP local:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Node.js (se tiver http-server instalado)
   npx http-server
   ```
4. Abra `http://localhost:8000` no navegador

## Funcionalidades Técnicas

### Renderização 3D
- Geometria realista do copo com cilindro cônico
- Material Phong com iluminação avançada
- Sombras suaves e reflexos
- **Sistema de texturas em camadas**: Base + Usuário
- Combinação procedural de texturas usando Canvas API

### Controles Interativos
- Rotação limitada para evitar inversão
- Zoom com limites mínimo e máximo
- Suporte completo a touch para mobile
- Responsividade automática do canvas

### Interface
- Design inspirado em aplicativos modernos
- Feedback visual para cada etapa
- Preview da imagem com opção de remoção
- Estados visuais para upload e carregamento

## Compatibilidade

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Dispositivos móveis iOS/Android

## Melhorias Futuras

- [ ] Múltiplas texturas/designs
- [ ] Exportação da visualização 3D
- [ ] Diferentes tipos de copos
- [ ] Cores personalizáveis
- [ ] Integração com APIs de impressão

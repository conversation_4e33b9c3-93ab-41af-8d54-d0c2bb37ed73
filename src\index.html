<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personalizador de Copo</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/loaders/GLTFLoader.js"></script>
</head>
<body>
    <div class="container">
        <div class="step completed">
            <div style="display: flex; align-items: center; gap: 16px;">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">Baixe o gabarito e crie sua arte</div>
                </div>
            </div>
            <button class="download-btn" id="downloadBtn">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                    <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                </svg>
                <span>Baixar gabarito</span>
            </button>
        </div>

        <div class="step">
            <div class="step-number" id="step2Number">2</div>
            <div class="step-content">
                <div class="step-title">Faça o upload de sua arte</div>
            </div>
        </div>

        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <input type="file" id="fileInput" accept="image/*">
                <div class="upload-content" id="uploadContent">
                    <button class="upload-btn" id="uploadBtn">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M.5 6.9a.5.5 0 0 1 .5-.5v-2.5a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v2.5a.5.5 0 0 1-1 0v-2.5H2v2.5a.5.5 0 0 1-.5.5z"/>
                            <path d="M7.646 4.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707V14.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708l3-3z"/>
                        </svg>
                        Enviar arquivo
                    </button>
                    <div class="upload-constraints">JPG ou PNG, até 10MB</div>
                </div>
                <div class="upload-preview hidden" id="uploadPreview">
                    <img id="previewImage" alt="Preview da imagem">
                    <button class="remove-image" id="removeImage">×</button>
                </div>
            </div>
        </div>

        <div class="preview-section">
            <div class="cup-preview">
                <div class="cup-placeholder" id="cupPlaceholder">
                    <div class="placeholder-icon">📁</div>
                    <div class="placeholder-text">Faça upload de uma imagem</div>
                </div>
                <canvas class="cup-canvas hidden" id="cupCanvas"></canvas>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>